:root {
  --primary-blue: #4169E1;        
  --primary-dark: rgba(16, 163, 127, 1);         
  --primary-light: rgba(16, 163, 127, 0.3);
  --sidebar-bg: #141924;
  --main-bg: #0b0f17;

  --primary-color: var(--primary-dark);

  /* Typography Scale */
  --font-size-xl: 22px;
  --font-size-lg: 20px;
  --font-size-md: 16px;
  --font-size-sm: 14px;

  /* Font Weights */
  --font-regular: 400;
  --font-medium: 500;
  --font-semibold: 800;
  --font-bold: 1000;

  /* Line Heights */
  --line-height-tight: 1.2;   
  --line-height-normal: 1.3;  
  --line-height-relaxed: 1.5; 

}

body {
  background-color: var(--main-bg);
  font-family: 'Inter', sans-serif;
  overflow: hidden;
  min-width: 320px;
  font-size: var(--font-size-md);
  font-weight: var(--font-regular);
  line-height: var(--line-height-relaxed);
}

h1, .h1 {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-semibold);
  line-height: var(--line-height-normal);
}

h2, .h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-semibold);
  line-height: var(--line-height-normal);
}

h3, .h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-semibold);
  line-height: var(--line-height-normal);
}

.header-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-medium);
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-semibold);
}

.selected-domain-text {
  font-size: var(--font-size-md);
  font-weight: var(--font-medium);
}

.helper-text {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

.message-text {
  font-size: var(--font-size-md);
  line-height: var(--line-height-relaxed);
}

.message-header {
  font-size: var(--font-size-xl);
  font-weight: var( --font-bold);
  line-height: var(--line-height-normal);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-semibold);
  line-height: var(--line-height-normal);
}

.domain-header h5 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-semibold);
}

.user-email {
  font-size: var(--font-size-sm);
  font-weight: var(--font-medium);
}

.user-status {
  font-size: var(--font-size-xs);
}

.file-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-regular);
}

.document-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-medium);
}

.page-number {
  font-size: var(--font-size-sm);
}

.open-file-btn,
.new-domain-button,
.select-button {
  font-size: var(--font-size-sm);
  font-weight: var(--font-medium);
  line-height: var(--line-height-tight);
}

input, textarea, select {
  font-size: var(--font-size-md);
  line-height: var(--line-height-relaxed);
}

::placeholder {
  font-size: var(--font-size-md);
}

.resource-table th {
  font-size: var(--font-size-xs);
  font-weight: var(--font-medium);
}

.resource-table td {
  font-size: var(--font-size-xs);
  font-weight: var(--font-regular);
}

.open-file-btn:hover,
.select-button,
.upload-btn,
.btn-submit {
  background: var(--primary-blue);
}

.open-file-btn:hover,
.select-button:hover,
.upload-btn:hover,
.btn-submit:hover {
  background: var(--primary-dark);
}

.empty-folder,
.sidebar-file-list-icon,
.file-icon i,
.bullet-number {
  color: var(--primary-dark);
}

.checkbox-label,
.domain-card.selected,
.new-domain-input-card,
.sources-box {
  border-color: var(--primary-blue);
}

.upload-icon-wrapper:hover,
.new-domain-button:hover,
.menu-item:hover {
  background: var(--primary-light);
}

.user-status,
.premium-link,
.text-primary-blue {
  color: var(--primary-blue);
}

.progress-bar {
  background: var(--primary-blue);
}

.plan-badge {
  background-color: var(--primary-light);
  color: var(--primary-blue);
  border-color: var(--primary-blue);
}

.sources-box {
  background-color: var(--primary-light);
  border: 1px solid var(--primary-blue);
  color: var(--primary-blue);
}

.user-section:hover {
  border-color: var(--primary-blue);
}


.alert-icon i {
  border-color: var(--primary-blue);
}

.file-item.uploaded {
  border-color: var(--primary-blue);
  background: var(--primary-light);
}

.domain-name {
  color: var(--primary-blue);
}

.bullet-number {
  border-color: var(--primary-blue);
}

.bullet-line {
  background: linear-gradient(180deg,
    var(--primary-blue) 0%,
    rgba(65, 105, 225, 0.1) 100%);
}

@keyframes colorPulse {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -100% 0;
  }
}

.premium-link {
  background: linear-gradient(90deg,
    transparent 0%,
    var(--primary-dark) 100%,
    transparent 50%);
}

.menu-trigger:hover,
.resources-trigger:hover {
  background: var(--primary-light);
}

.form-control:focus {
  border-color: var(--primary-dark);
}

.sidebar,
.top-header {
  display: block;
  align-items: center;
}

.top-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
}

.sidebar {
  position: relative;
  height: 100%;
}

.px-4.py-3 {
  background: transparent;
}

.upload-container {
  position: relative;
  padding: 0 24px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-loading-overlay {
  margin-top: 16px;
  background: rgba(33, 33, 33, 0.95);
  border-radius: 12px;
  padding: 24px;
  display: none; 
  flex-direction: column;
  align-items: center;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-content h5 {
  margin: 16px 0 8px 0;
  font-size: 18px;
}

.loading-content p {
  margin: 0;
  font-size: 14px;
}

.loading-content p.text-secondary {
  color: rgba(255, 255, 255, 0.6) !important;
  font-size: 12px;
  margin-top: 4px;
}

/* Adjust spinner size */
.spinner-border {
  width: 2.5rem;
  height: 2.5rem;
  border-width: 0.25em;
}

/* Ensure file list remains visible */
.file-list {
  position: relative;
  z-index: 2;
}

/* Adjust the upload button to ensure it's above the loading overlay */
.upload-btn {
  position: relative;
  z-index: 2;
}

.drive-select-btn {
  padding: 12px;
  background-color: transparent;
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  color: #10B981;
  transition: all 0.3s ease;
  font-weight: 500;
}

.drive-select-btn:hover {
  background-color: rgba(16, 185, 129, 0.1);
  transform: translateY(-1px);
}

.drive-select-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.drive-icon {
  width: 20px;
  height: 20px;
}

.section-divider {
  width: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  height: 100vh;
  position: absolute;
  top: 0;
}

.divider-after-sidebar {
  right: 0;
}

.divider-before-resources {
  left: 0;
}

.feedback-modal {
  display: none;
}

.logo-text {
  font-size: 30px;
  font-weight: 700;
  margin-top: 8px;
}

.logo-image {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.menu-trigger {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: var(--primary-dark);
  font-size: 24px;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 1051;
}

.menu-trigger:hover {
  background: rgba(16, 185, 129, 0.1);
}

.header-menu-button {
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  margin-left: 20px;
  color: white;
  transition: all 0.3s ease;
}

.header-menu-button:hover {
  color: var(--primary-dark);
  transform: scale(1.1);
}
.resources-trigger {
  position: absolute;
  right: 20px;
  background: transparent;
  border: none;
  color: var(--primary-dark);
  font-size: 24px;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 1051;
  display: none; /* Varsayılan olarak gizli */
}

.resources-trigger:hover {
  background: rgba(16, 185, 129, 0.1);
  transform: scale(1.05);
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 24px;
  position: relative;
  margin-bottom: 100px
}

.chat-content {
  background-color: var(--main-bg);
}

.chat-content::-webkit-scrollbar {
  width: 4px;
}

.chat-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.chat-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.message-actions {
  position: absolute;
  bottom: -20px; 
  right: 0px; 
  display: flex;
  justify-content: flex-end;
  padding: 4px 0;
}

.export-button {
  background-color: var(--primary-green);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  right: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.2s ease;
  position: absolute;
}

.export-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.export-button:hover {
  background-color: #0da271;
}

.export-button:not(:disabled):hover {
  background-color: var(--primary-green);
}

.export-counter {
  position: absolute;
  right: -27px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: var(--primary-green);
}

.selection-mark {
  position: absolute;
  top: 8px;
  right: 8px;
  background: transparent;
  border: none;
  padding: 4px;
  opacity: 0.5;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  z-index: 2;
  color: var(--primary-green);
}

.message-bubble.ai-bubble {
  background: #242c3c;
  padding: 0;
  position: relative;
  margin-right: 0;
}

.message-bubble .message-text {
  padding: 12px 16px;
}

.action-container {
  background: rgb(42, 42, 42);
  border-radius: 4px;
  padding: 6px 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.copy-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.copy-button:hover {
  color: #10B981;
}
.copy-button.copied {
  color: #10B981;
  pointer-events: none;
}
.copy-button i {
  font-size: 12px;
}

.action-text {
  font-size: 12px;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.sidebar-container {
  width: 294px;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background: var(--sidebar-bg);
  z-index: 1050;
  transition: transform 0.3s ease;
}

.sidebar-container:not(.open) {
  transform: translateX(-294px);
}

.sidebar-container.open {
  transform: translateX(0);
}

.sidebar-backdrop {
  display: none;
}


.sidebar-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.resources-container {
  width: 600px;
  height: 100vh;
  position: fixed;
  right: 0;
  top: 0;
  background: var(--main-bg);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

mark {
  background-color: rgba(16, 185, 129, 0.3);
  border-radius: 4px;
  padding: 1px 2px;
  color: #fff;
  font-weight: normal;
  border-bottom: 1px dotted rgba(16, 185, 129, 0.4);
  text-shadow: none;
  transition: all 0.2s ease;
}

.menu-button {
  color: var(--primary-dark);
  font-size: 24px;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.empty-folder {
  color: var(--primary-dark);
  font-size: 20px;
}

.settings-icon {
  color: white;
  font-size: 20px;
  cursor: pointer;
}

.settings-icon-container {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--primary-dark);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.settings-icon-container:hover {
  background: rgba(16, 185, 129, 0.1);
}

.domain-selection-helper {
  color: var(--primary-dark);
  font-size: 14px;
  margin-top: 8px;
  text-align: center;
  opacity: 0.8;
}

.d-flex.vh-100 {
  width: 100%;
  overflow: hidden;
}

.bi-folder, .bi-folder-fill, .empty-folder {
  color: var(--primary-dark) !important; 
}

.open-file-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  width: 100%;
  text-align: center;
  margin-top: 10%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.open-file-btn:hover {
  background: var(--primary-dark);
}

.helper-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  text-align: center;
  padding: 0 10px;
  /*padding-top: 3%;*/
}

.bottom-section {
  background: transparent;
  margin-top: auto;
  padding: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-badge {
  background-color: #1029b91a;
  color: var(--primary-dark);
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  border: 1px solid var(--primary-dark);
}

.user-section {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid #1029b91a;
}

.user-section:hover {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border: 1px solid #1029b91a;
}

.user-section.active {
  background: rgba(16, 185, 129, 0.05);
  border: 1px solid var(--primary-dark);
}

.user-section:hover .user-avatar,
.user-section.active .user-avatar {
  box-shadow: 0 0 0 2px var(--primary-dark);
}

.user-section:hover .user-status::before,
.user-section.active .user-status::before {
  box-shadow: 0 0 0 2px var(--primary-dark);
}

.user-section::after {
  content: '';
  position: absolute;
  right: 16px;
  top: 66.5%;
  width: 8px;
  height: 8px;
  border-right: 2px solid rgba(255, 255, 255, 0.6);
  border-bottom: 2px solid rgba(255, 255, 255, 0.6);
  transform: translateY(-50%) rotate(45deg);
  transition: all 0.3s ease;
}

.user-section.active::after {
  border-color: var(--primary-dark);
  transform: translateY(-50%) rotate(-135deg);
}

.user-section:hover::after {
  border-color: var(--primary-dark);
}

.user-menu {
  position: absolute;
  bottom: calc(100% + 12px);
  left: 0;
  right: 0;
  background: var(--sidebar-bg);
  border: 1px solid #1029b91a;
  border-radius: 12px;
  padding: 8px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(16, 185, 129, 0.1);
}

.user-menu::before {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  width: 12px;
  height: 12px;
  background: var(--sidebar-bg);
  border-right: 1px solid #1029b91a;
  border-bottom: 1px solid #1029b91a;
}

.user-section.active .user-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-section.active .menu-item {
  transform: translateX(0);
  opacity: 1;

}

.user-section.active .menu-item:nth-child(1) {
  transition-delay: 0.1s;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  transform: translateX(-10px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background: var(--primary-dark);
  opacity: 0;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background: rgba(16, 185, 129, 0.1);
  color: var(--primary-dark);
  padding-left: 20px;
}

.menu-item:hover::before {
  opacity: 1;
}

.menu-item i {
  font-size: 16px;
  transition: all 0.3s ease;
}

.menu-item:hover i {
  transform: scale(1.1);
  color: var(--primary-dark);
}

.menu-divider {
  height: 1px;
  background: linear-gradient(to right,
      rgba(255, 255, 255, 0.05),
      rgba(16, 185, 129, 0.2),
      rgba(255, 255, 255, 0.05));
  margin: 8px 0;
}

.logout-item {
  color: #ff4d4d;
}

.logout-item:hover {
  background: rgba(255, 77, 77, 0.1);
  color: #ff4d4d;
}

.logout-item:hover i {
  color: #ff4d4d;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.user-avatar.has-image {
  padding: 0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  box-shadow: none;
}

.user-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.user-info {
  flex: 1;
}

.user-email {
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
}

.user-status {
  color: var(--primary-dark);
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.user-status::before {
  content: "";
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: var(--primary-dark);
  border-radius: 50%;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

.bottom-links {
  display: flex;
  gap: 24px;
  justify-content: center;
}

.bottom-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
}

.bottom-links .premium-link {
  position: relative;
  color: var(--primary-dark);
  font-weight: 500;
  animation: colorPulse 2s infinite;
  background: linear-gradient(90deg,
      transparent 0%,
      var(--primary-dark) 100%,
      transparent 50%);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes colorPulse {
  0% {
    background-position: 100% 0;
  }

  100% {
    background-position: -100% 0;
  }
}

.bottom-links a:not(.premium-link):hover {
  color: var(--primary-dark);
}

/* Bottom Search Section Styles */
.bottom-message-section {
  position: sticky;
  bottom: 0;
  background-color: var(--main-bg);
  padding: 24px;
  z-index: 10;
  width: 100%;
  margin-top: auto;
}

.message-container {
  display: flex;
  gap: 12px;
  align-items: center;
  width: calc(100% - 600px - 294px);
  position: fixed;
  bottom: 24px;
  padding: 0 24px;
  left: 294px;
  transition: all 0.3s ease;
}

.sidebar-closed .message-container {
  width: calc(100% - 600px);
  left: 0; 
}

.sources-box {
  background-color: #1029b91a;
  border: 1px solid var(--primary-dark);
  border-radius: 12px;
  width: 132px;
  height: 53px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-dark);
  font-family: 'Inter', sans-serif;  
  font-size: 14px;                  
  font-weight: 500;                 
  font-size: 16px;
  font-weight: 500;
  flex-shrink: 0;
  gap: 4px;
}

.sources-number {
  display: inline-block;
}

.message-input-container {
  position: relative;
  width: 95%;
  display: flex;
  align-items: center;
  background-color: var(--sidebar-bg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 8px 16px;
  transition: all 0.3s ease;
  height: 53px;
}

.message-input {
  width: calc(100% - 90px);
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 12px 0;
  color: white;
  font-family: 'Inter', sans-serif;  
  font-size: 14px;                   
  line-height: 1.5;                  
  font-size: 16px;
  line-height: 1.5;
  resize: none;
  height: 53px;
  overflow-y: hidden;
}

.message-input:focus {
  outline: none;
  border-color: transparent;
}

.message-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.send-icon {
  color: white;
  font-size: 20px;
}

.menu-button {
  font-size: 35px;
}

.modal-content {
  background: var(--sidebar-bg);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.selected-domain-text {
  max-width: 130px;
  /* Adjust this value based on your needs */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}

.message-content {
  font-size: var(--font-size-sm);
  color: rgb(221, 225, 231);
  line-height: 1.3;
  width: 100%;
  overflow-x: auto;
  font-weight: 300;
}

.message-content pre,
.message-content code {
  max-width: 100%;
  white-space: pre-wrap;
}

.message-item {
  margin: 8px 0;
  padding-left: 24px;
  position: relative;
  font-size: var(--font-size-md);
  font-weight: var(--font-semibold);
  color: rgb(221, 225, 231);
}

.message-item:before {
  content: "-";
  position: absolute;
  left: 8px;
}

.message-item.nested-1 {
  margin-left: 24px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-regular);
}

.message-item.nested-2 {
  margin-left: 48px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-regular);
}

.message-header {
  color: rgba(255, 255, 255,1);
  font-size: var(--font-size-xl);
  font-weight: var(--font-bold);
  margin: 12px 0 8px 0;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(16, 185, 129, 0.2);
}

.message-bold {
  color: rgba(255, 255, 255,1);
  font-weight: var(--font-semibold);
}

.message-substance {
  display: block;
  margin: 2px 0 2px 16px; /* Added top/bottom margin */
  color: rgba(255, 255, 255, 0.8);
}

.message-bullet {
  display: block;
  margin: 2px 0; /* Reduced from 4px to 2px */
  padding-left: 16px;
}

.message-bullet-nested {
  margin-left: 16px;
}

.chat-disabled .message-input-container {
  opacity: 0.5;
  pointer-events: none;
}

.chat-disabled .message-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.d-flex.justify-content-between.align-items-center.mb-3 {
  overflow: hidden;
  /* Ensures the container doesn't grow */
}

.domain-modal-wrapper {
  padding: 24px;
  color: white;
}

.domain-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.domain-header h5 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.close-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 24px;
  padding: 4px;
  transition: all 0.3s ease;
}

.close-button:hover {
  color: white;
}

.domain-search {
  position: relative;
  margin-bottom: 20px;
}

.domain-search i {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.4);
}

.domain-search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  transition: all 0.3s ease;
}

.domain-search-input:focus {
  outline: none;
  border-color: var(--primary-dark);
  background: rgba(255, 255, 255, 0.08);
}

.domain-search-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.domains-container {
  max-height: 320px;
  overflow-y: auto;
  margin-bottom: 20px;
  padding-right: 4px;
}

.domains-container::-webkit-scrollbar {
  width: 4px;
}

.domains-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.domains-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.domain-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;
}

.domain-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.domain-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.domain-checkbox {
  display: none;
}

.checkbox-label {
  display: block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--primary-dark);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}


.domain-checkbox:checked+.checkbox-label {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
}

.domain-checkbox:checked+.checkbox-label:after {
  display: block;
}

.domain-info h6 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-count {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.delete-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.4);
  padding: 8px;
  transition: all 0.3s ease;
  padding: 8px;
}

.delete-file-btn {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.4);
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: auto;
  /* Pushes button to the right */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.delete-file-btn:hover {
  color: #ff4d4d;
}

.delete-button:hover {
  color: #ff4d4d;
}

.new-domain-button {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px dashed rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.new-domain-button:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--primary-dark);
  color: var(--primary-dark);
}

.select-button {
  width: 100%;
  padding: 14px;
  background: var(--primary-dark);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.select-button:hover {
  background: var(--primary-light);
  transform: translateY(-1px);
}

.new-domain-input-card {
  background: rgba(16, 185, 129, 0.1) !important;
  border-color: var(--primary-dark) !important;
}

.new-domain-input {
  background: transparent;
  border: none;
  color: white;
  font-size: 16px;
  padding: 8px;
  width: 100%;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.new-domain-input:focus {
  outline: none;
  white-space: normal;
  overflow: visible;
}

.domain-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  z-index: 2;
}

.new-domain-actions {
  display: flex;
  gap: 8px;
}

.edit-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.4);
  padding: 8px;
  transition: all 0.3s ease;
}

.edit-button,
.delete-button {
  cursor: pointer;
  z-index: 2;
}

.edit-button:hover {
  color: var(--primary-dark);
}

.domain-name-input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--primary-dark);
  border-radius: 4px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  padding: 4px 8px;
  width: 100%;
  max-width: 200px;
  padding-right: 65px;
}

.domain-name-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.08);
}

.domain-edit-actions {
  position: absolute;
  right: 8px;
  display: flex;
  gap: 4px;
  align-items: center;
}

.domain-name-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 200px;
}

.edit-confirm-button,
.edit-cancel-button {
  background: transparent;
  border: none;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-confirm-button {
  color: var(--primary-dark);
}

.edit-confirm-button:hover {
  transform: scale(1.1);
}

.edit-cancel-button {
  color: #ff4d4d;
}

.edit-cancel-button:hover {
  transform: scale(1.1);
}

.confirm-button,
.cancel-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  padding: 8px;
  transition: all 0.3s ease;
}

.confirm-button:hover {
  color: var(--primary-dark);
}

.cancel-button:hover {
  color: #ff4d4d;
}

.domain-card.filtered {
  display: none;
}

.limit-indicator {
  background: rgba(255, 255, 255, 0.02);
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.limit-indicator .progress {
  border-radius: 4px;
  overflow: hidden;
}

.limit-indicator .progress-bar {
  transition: width 0.3s ease, background-color 0.3s ease;
}

.bg-warning {
  background-color: #ffc107 !important;
}

.limit-indicator small {
  font-size: 12px;
}

.limits-container {
  padding: 1rem 0;
}

.upgrade-button {
  background-color: var(--primary-dark);
  color: white;
  border: none;
  padding: 8px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.upgrade-button:hover {
  background-color: var(--primary-light);
  transform: translateY(-1px);
}

.text-white-50 {
  font-size: 11px;
  opacity: 0.5;
}

#deleteConfirmModal .modal-backdrop.show {
  backdrop-filter: none !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

.modal-open .modal-backdrop+.modal-backdrop {
  display: none;
}

#deleteConfirmModal .modal-content {
  background: var(--sidebar-bg);
  color: white;
}

#deleteConfirmModal .btn-outline-secondary {
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
}

#deleteConfirmModal .btn-outline-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
}

#deleteConfirmModal .btn-danger {
  background: #dc3545;
  border: none;
}

#deleteConfirmModal .btn-danger:hover {
  background: #bb2d3b;
}

/* Modal stacking fixes */
#deleteConfirmModal {
  z-index: 1060 !important; /* Higher than domain modal */
}

#deleteConfirmModal .modal-backdrop {
  z-index: 1059 !important;
}

.modal-backdrop + .modal-backdrop {
  display: none !important; /* Prevent multiple backdrops */
}

/* Ensure delete modal content stays on top */
#deleteConfirmModal .modal-content {
  position: relative;
  z-index: 1061 !important;
}

/* Fix for domain modal when delete modal is open */
#domainSelectModal.delete-confirmation-open {
  overflow-y: hidden !important;
}

#domainSelectModal.delete-confirmation-open .modal-content {
  filter: blur(2px);
  transition: filter 0.2s ease;
}

#fileUploadModal .modal-dialog {
  max-width: 600px;
}

/* Modal Header Styling */
#fileUploadModal .modal-header {
  padding: 20px 24px 10px;
}

#fileUploadModal .modal-header h6 {
  font-size: 20px;
  font-weight: 500;
}

.upload-container {
  padding: 0 24px 24px;
}

.upload-area {
  border: 2px dashed rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 48px 30px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.02);
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-icon-wrapper {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--primary-dark);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  cursor: pointer;
  background: rgba(16, 185, 129, 0.05);
  transition: all 0.3s ease;
}

.upload-icon-wrapper:hover {
  background: rgba(16, 185, 129, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
}

.upload-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.upload-icon-wrapper:hover .upload-icon {
  transform: scale(1.1);
}

.upload-area.dragover {
  border-color: var(--primary-dark);
  background: rgba(16, 185, 129, 0.05);
}

.upload-icon i {
  font-size: 60px;
  margin: 0;
}

.choose-text {
  cursor: pointer;
  transition: color 0.3s ease;
}

.choose-text:hover {
  color: #0da271;
}

.text-primary-green {
  color: var(--primary-dark);
}

.domain-name {

  font-weight: 500;
}

.upload-content h5 {
  font-size: 28px;
  margin-bottom: 16px;
}

.upload-content p {
  font-size: 16px;
  margin-bottom: 20px;
}

.upload-content small {
  font-size: 14px;
  opacity: 0.7;
}

.file-list {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
}

.file-list::-webkit-scrollbar {
  width: 4px;
}

.file-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.file-list::-webkit-scrollbar-thumb {
  background: var(--primary-dark);
  border-radius: 2px;
}

.file-list::-webkit-scrollbar-thumb:hover {
  background: #0da271;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.file-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.file-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin-right: 8px;
}

.sidebar-file-list-icon {
  font-size: 1.25rem;
  margin-right: 0.75rem;
  color: var(--primary-dark);
  flex-shrink: 0;
  height: 0;
  margin-right: -8px;


}

.file-icon i {
  font-size: 24px;
}

.file-info {
  flex-grow: 1;
  overflow: hidden;
}

.file-name {
  margin-left: 8px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-progress {
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1px;
  overflow: hidden;
  margin-top: 4px;
}

.progress-bar {
  height: 100%;
  background: var(--primary-dark);
  width: 0;
  transition: width 0.3s ease;
}

.file-remove {
  padding: 8px;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: color 0.3s ease;
  opacity: 1;
}

.file-item.pending-upload .file-remove {
  opacity: 1;
  pointer-events: auto;
}

/* Uploading state */
.file-item.uploading .file-remove,
.file-item.uploaded .file-remove {
  opacity: 0;
  pointer-events: none;
  width: 0;
  margin: 0;
  padding: 0;
}

.file-item.uploaded {
  border-color: var(--primary-dark);
  background: rgba(16, 185, 129, 0.05);
}

.uploading .file-remove {
  opacity: 0.5;
  pointer-events: none;
}

.file-remove:hover {
  color: #ff4d4d;
}

.upload-btn {
  width: 100%;
  padding: 12px;
  background: var(--primary-dark);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.upload-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
}

.upload-btn:not(:disabled):hover {
  background: #0da271;
}

/* Add File Button */
.add-file-btn {
  width: 100%;
  padding: 12px;
  background: transparent;
  border: 2px dashed rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: var(--primary-dark);
  margin-top: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.add-file-btn:hover {
  background: rgba(16, 185, 129, 0.05);
  border-color: var(--primary-dark);
}

.add-file-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.add-file-btn i {
  font-size: 20px;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  margin-left: 294px;
  width: calc(100% - 894px);
  transition: all 0.3s ease;
}

.chat-container.expanded {
  margin-left: 0;
}

.chat-container.sidebar-closed {
  margin-left: 0;
  width: calc(100% - 600px);
}

.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chat-message {
  display: flex;
}

.chat-message.user {
  justify-content: flex-end;
}

.chat-message.ai {
  justify-content: flex-start;
}

.chat-message.ai.selected .selection-mark {
  opacity: 1;
}

.message-bubble {
  max-width: 90%;
  background-color: #141924;
  border-radius: 12px;
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

.ai-bubble {
  background: #242c3c;
}

.message-text {
  font-family: 'Inter', sans-serif;  
  font-size: 14px;                  
  line-height: 1.5;                 
  color: #FFFFFF;
}

.message-time {
  font-size: 12px;
  color: #CCCCCC;
  text-align: right;
  margin-top: 4px;
}

/* Sidebar Files List Container */
.sidebar-files {
  flex: 1;
  padding: 0;
  margin-right: 10%;
  text-align: center;
}

/* Scrollbar Styling */
.sidebar-files::-webkit-scrollbar {
  width: 4px;
}

.sidebar-files::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.sidebar-files::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

/* Individual File Item */
.sidebar-file-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.sidebar-file-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

/* File Icon */
.sidebar-file-item .file-icon {
  font-size: 1.25rem;
  margin-right: 0.75rem;
  color: var(--primary-dark);
  flex-shrink: 0;
}

/* File Name */
.sidebar-file-item .file-name {
  color: #fff;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/* File Actions */
.sidebar-file-item .file-actions {
  margin-left: auto;
  flex-shrink: 0;
}

.action-dots {
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0.25rem;
  transition: color 0.3s ease;
}

.action-dots:hover {
  color: var(--primary-dark);
}

/* Action Menu */
.action-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--sidebar-bg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  min-width: 160px;
  z-index: 1000;
  display: none;
  margin-top: 0.5rem;
}

.action-menu.show {
  display: block;
}

.action-menu-item {
  padding: 0.75rem 1rem;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--primary-dark);
}

.action-menu-item i {
  font-size: 1rem;
}

/* Rename Input */
.rename-input {
  background: transparent;
  border: none;
  border-bottom: 1px solid var(--primary-dark);
  color: white;
  font-size: 0.875rem;
  padding: 0.25rem
}

;

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal.show .modal-dialog {
  animation: modalFadeIn 0.3s ease-out;
}



/* Remove default list styles */


.sidebar-files li {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  margin: 8px 0;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  text-align: left;
  width: 114.5%;
  padding-left: 4px;
}

.sidebar-files li .d-flex {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
}

.icon-container {
  position: relative;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.delete-confirm-actions {
  position: absolute;
  display: none;
  align-items: center;
  gap: 4px;
  background: var(--sidebar-bg);
  border-radius: 4px;
  padding: 2px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 10;
  margin-left: 23px;
}

.delete-confirm-actions.show {
  display: flex;
}

.confirm-delete-btn,
.cancel-delete-btn {
  background: transparent;
  border: none;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-delete-btn {
  color: var(--primary-dark);

}

.cancel-delete-btn {
  color: #ff4d4d;
}

.confirm-delete-btn:hover {
  transform: scale(1.1);
}

.cancel-delete-btn:hover {
  transform: scale(1.1);
}

.sidebar-files li .sidebar-file-list-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: var(--primary-dark);
  font-size: 1.25rem;
  transition: opacity 0.2s ease;
}

.sidebar-files li .file-name {
  order: 2;
  flex: 1;
  margin-left: 0;
}

.sidebar-files li .delete-file-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  background: transparent;
  border: none;
  padding: 0;
  color: rgba(255, 255, 255, 0.6);
  transition: opacity 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.sidebar-files li .checkbox-wrapper {
  position: absolute;
  right: 0;
  width: 20px;
  height: 20px;
}

.sidebar-files li .file-checkbox {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  cursor: pointer;
}

.sidebar-files li .checkbox-label {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid var(--primary-dark);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.sidebar-files li .file-checkbox:checked+.checkbox-label {
  background: var(--primary-dark);
}

.sidebar-files li .file-checkbox:checked+.checkbox-label:after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.sidebar-files li:hover .delete-file-btn {
  opacity: 1;
  z-index: 2;
}

.sidebar-files li:hover .sidebar-file-list-icon {
  opacity: 0;
}

.sidebar-files li .delete-file-btn:hover {
  color: #ff4d4d;
}

/* Show/hide on hover */
.sidebar-files li:hover .delete-file-btn {
  opacity: 1;
}

.sidebar-files li:hover .sidebar-file-list-icon {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: opacity 0.2s ease;
}

.sidebar-files li .delete-file-btn:hover {
  color: #ff4d4d;
}

.sidebar-files li:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

/* File type icon */
.sidebar-files li::before {
  color: var(--primary-dark);
  margin-right: 12px;
  font-size: 16px;
}

.sidebar-files li .file-name {
  flex: 1;
  margin-left: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 32px;
}

.file-actions {
  position: absolute;
  right: 7%;
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: 1;
  margin-left: 0;
}


.sidebar-files li:hover .file-actions {
  display: block;
  opacity: 1;
}

/* Actions menu */
.action-menu {
  position: absolute;
  right: 0;
  top: 100%;
  background: var(--sidebar-bg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 0;
  min-width: 120px;
  z-index: 1000;
  display: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
}

.action-menu.show {
  display: block;
  position: absolute;
  margin-top: 5px;
}

.action-menu-item {
  padding: 8px 16px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--primary-dark);
}

/* File name overflow handling */
.file-name {
  color: white;
  font-size: 14px;
  margin-left: 8px;
}

/* Hover effects */
.action-menu-item i {
  font-size: 14px;
}

/* Rename input styling */
.rename-input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--primary-dark);
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  font-size: 14px;
  width: calc(100% - 40px);
}

.rename-input:focus {
  outline: none;
  border-color: var(--primary-dark);
  background: rgba(255, 255, 255, 0.08);
}

.file-add {
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 16px;
  background-color: transparent;
  z-index: 10;
}

.file-list-container {
  display: flex;
  flex-direction: column;
  position: relative;
  top: 0;
  max-height: calc(100vh - 455px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* Custom scrollbar */
.file-list-container::-webkit-scrollbar {
  width: 4px;
}

.file-list-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.file-list-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.file-list-container::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Resource item styles */

.resource-content {
  background-color: var(--main-bg);
  padding: 24px 32px;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.05) rgba(255, 255, 255, 0.05);
}

.resource-content {
  scrollbar-width: thin;
  /* For Firefox */
  scrollbar-color: rgba(255, 255, 255, 0.05) rgba(255, 255, 255, 0.05);
  /* For Firefox */
}

/* Webkit (Chrome, Safari, Edge) scrollbar customization */
.resource-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.resource-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.resource-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.resource-content::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Ensure smooth scrolling */
.resource-content {
  scroll-behavior: smooth;
}

/* Optional: Add styles for when scrollbar is active */
.resource-content::-webkit-scrollbar-thumb:active {
  background: rgba(255, 255, 255, 0.05);
}

/* Optional: Style the corner where vertical and horizontal scrollbars meet */
.resource-content::-webkit-scrollbar-corner {
  background: var(--main-bg);
}

/* For Firefox compatibility */
@supports (scrollbar-color: auto) {
  .resource-content {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.306) rgba(255, 255, 255, 0.05);
  }
}

/* For touch devices */
@media (hover: none) {
  .resource-content {
    scrollbar-width: none;
    /* Hide scrollbar on touch devices */
    -ms-overflow-style: none;
    /* Hide scrollbar in IE/Edge */
  }

  .resource-content::-webkit-scrollbar {
    display: none;
    /* Hide scrollbar on touch devices */
  }
}

.resources-list {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.resources-placeholder {
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
  padding: 2rem;
  font-size: 0.95rem;
  line-height: 1.5;
}

.resource-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.resources-placeholder {
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
  padding: 2rem;
  font-size: 0.95rem;
  line-height: 1.5;
}

.resource-table {
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
  background-color: var(--sidebar-bg);
  border-radius: 8px;
  overflow: hidden;
  font-size: 10px;
  scale: 1;
}

.resource-table th,
.resource-table td {
  padding: 4px;
  text-align: left;
  border: 1px solid rgba(255, 255, 255, 0.1);
  white-space: nowrap;
}

.resource-table th {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--primary-dark);
  font-weight: 500;
  font-size: 12px; 
}

.resource-table td {
  font-size: 11px;  /* Slightly smaller for data cells */
}

.resource-table tr:hover {
  background-color: rgba(255, 255, 255, 0.051);
}

.resource-table .align-left {
  text-align: left;
}

.resource-table .indent-cell {
  padding-left: 16px;
}

.resource-table .numeric {
  font-weight: 500;
}

.resource-table .identifier {
  color: rgba(255, 255, 255, 0.8);
}

.description {
  overflow-x: auto;
  max-width: 100%;
  padding-bottom: 4px; /* Space for potential scrollbar */
}

.description-content {
  margin-bottom: 10px;
}

.table-wrapper {
  margin: 10px 0;
}

.description > :last-child {
  margin-bottom: 0;
}

/* Mobile responsiveness for tables */
@media (max-width: 768px) {
  .resource-table {
      display: block;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
  }
}

.source-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.document-name {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
}

.page-number {
  display: flex;
  align-items: center;
  gap: 4px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.page-number i {
  font-size: 12px;
}

.content-wrapper {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.bullet-indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 4px;
}

.bullet-line {
  position: absolute;
  left: 50%;
  height: calc(100% + 44px);
  /* Adjusts the line height to connect to the next item */
  width: 2px;
  background: linear-gradient(180deg,
      var(--primary-dark) 0%,
      rgba(16, 185, 129, 0.1) 100%);
  z-index: 1;
}

.resource-item:last-child .bullet-line {
  display: none;
}

.bullet-number {
  position: relative;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--main-bg);
  border: 2px solid var(--primary-dark);
  color: var(--primary-dark);
  font-size: 12px;
  font-weight: 500;
  border-radius: 50%;
  z-index: 2;
}

.description {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  padding-top: 2px;
}

/* Hover effects */
.resource-item:hover .bullet-number {
  background: var(--primary-dark);
  color: #fff;
  transform: scale(1.05);
  transition: all 0.3s ease;
}

.resource-item:hover .description {
  color: #fff;
  transition: color 0.3s ease;
}

.alert-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1100;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.alert-modal.show {
  display: flex;
  opacity: 1;
}

.alert-content {
  background: var(--sidebar-bg);
  padding: 32px;
  border-radius: 16px;
  text-align: center;
  max-width: 400px;
  width: 90%;
  border: 1px solid rgba(16, 185, 129, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.alert-modal.show .alert-content {
  transform: scale(1);
}

.alert-icon {
  margin-bottom: 20px;
}

.alert-icon i {
  font-size: 41px;
  background: rgba(16, 185, 129, 0.1);
  padding: 13px;
  border-radius: 31%;
  border: 2px solid var(--primary-dark);
}

.alert-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
  color: white;
}

.alert-message {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
}

.alert-button {
  background: var(--primary-dark);
  border: none;
  color: white;
  padding: 12px 32px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.alert-button:hover {
  background: var(--primary-light);
  transform: translateY(-1px);
}

.feedback-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1100;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feedback-modal.show {
  display: flex;
  opacity: 1;
}

.feedback-modal-content {
  background: var(--sidebar-bg);
  padding: 32px;
  border-radius: 16px;
  max-width: 500px;
  width: 90%;
  border: 1px solid rgba(16, 185, 129, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  transform: scale(0.9);
  transition: transform 0.3s ease;
  color: white;
}

.feedback-modal.show .feedback-modal-content {
  transform: scale(1);
}

/* Modal Header */
.feedback-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  position: relative;
}

.feedback-modal-header h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.close-modal {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-modal:hover {
  color: white;
}

/* Modal Description */
.feedback-modal-description {
  margin-bottom: 24px;
  color: rgba(255, 255, 255, 0.8);
}

.feedback-modal-description ol {
  padding-left: 20px;
}

.feedback-modal-description li {
  margin-bottom: 8px;
}

/* Form Elements */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: white !important;
  /* Force white text */
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-dark);
  background: rgba(255, 255, 255, 0.08);
  color: white !important;
  box-shadow: none;
}

select.form-control {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.05);
}

select.form-control option {
  background-color: var(--sidebar-bg);
  color: white;
}

.form-control[type="file"] {
  padding: 0;
  border: none;
  background: transparent;
}

.form-control[type="file"]::-webkit-file-upload-button {
  visibility: hidden;
  display: none;
}

.form-control[type="file"]::before {
  content: 'Choose files';
  display: inline-block;
  padding: 8px 16px;
  border: 1px solid var(--primary-dark);
  border-radius: 6px;
  color: var(--primary-dark);
  background: rgba(16, 185, 129, 0.1);
  outline: none;
  white-space: nowrap;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.form-control[type="file"]:hover::before {
  background: rgba(16, 185, 129, 0.2);
}

/* Responsive Layout */
.feedback-modal-content {
  max-width: min(500px, 90vw);
  width: 90%;
  max-height: min(90vh, 800px);
  overflow-y: auto;
}

textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

.form-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
}

/* Footer Buttons */
.feedback-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
}

.btn-cancel {
  padding: 10px 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: transparent;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.1);
}

.btn-submit {
  padding: 10px 24px;
  background: var(--primary-dark);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-submit:hover {
  background: var(--primary-light);
  transform: translateY(-1px);
}

#ratingModal .modal-dialog {
  max-width: 400px;
  margin: 1.75rem auto;
}

#ratingModal .modal-content {
  background-color: rgb(11, 15, 23) !important;
  border-radius: 12px;
  border: 1px solid rgb(11, 15, 23);
}

#ratingModal .domain-modal-wrapper {
  padding: 24px;
}

#ratingModal .modal-header {
  padding: 0;
  margin-bottom: 20px;
  border: none;
  display: flex;
  justify-content: space-between;
  align-items: center;

}

#ratingModal .modal-title {
  color: #0da271;
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  width: 100%;
  text-align: center;
}

#ratingModal .close-button {
  background: transparent;
  border: none;
  color: rgb(255, 255, 255, 0.6);
  font-size: 24px;
  padding: 0;
  margin: -8px -8px 0 0;
  cursor: pointer;
  transition: color 0.2s;
}

#ratingModal .close-button:hover {
  color: #fff;
}

#ratingModal .modal-body {
  padding: 0;
}

/* Stars Container Styles */
.stars-container {
  padding: 0.3rem 0;
}

.stars {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.stars i {
  font-size: 2.5rem;
  color: #6C757D;
  cursor: pointer;
  transition: all 0.2s ease;
}

.stars i:hover {
  transform: scale(1.1);
  color: #0da271;
}

.stars i.active {
  color: #0da271;
}

.rating-labels {
  display: flex;
  justify-content: space-between;
  color: #0da271;
  padding: 0 1.5rem;
  font-size: 14px;
}

.feedback-container {
  margin-top: 1rem;
}

.feedback-input {
  width: 100%;
  background-color: rgb(11, 15, 23);
  border: 1px solid rgb(45, 55, 72);
  border-radius: 8px;
  color: #fff;
  padding: 20px;
  resize: none;
  height: 150px;
  font-size: 14px;
}

.feedback-input:focus {
  outline: none;
  border-color: #0da271;
  height: 150px;
}

.feedback-input::placeholder {
  color: rgb(255, 255, 255, 0.6);
}

.submit-button {
  background-color: var(--primary-dark);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 24px;
  font-size: 16px;
  cursor: pointer;
  margin-top: 16px;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background-color: rgb(13, 162, 113);
}

/* Modal Animation */
.modal.fade .modal-dialog {
  transform: scale(0.8);
  transition: transform 0.2s ease-in-out;
}

.modal.show .modal-dialog {
  transform: scale(1);
}

@media (max-width: 1600px) {
  .resources-container {
    width: 350px; /* More aggressive reduction */
  }

  /* Expand the chat container to use the available space */
  .chat-container {
    width: calc(100% - 294px - 350px); /* sidebar width + resources width */
  }

  .chat-container.sidebar-closed {
    width: calc(100% - 350px);
    margin-left: 0;
  }

  /* Adjust message container to match new widths */
  .message-container {
    width: calc(100% - 350px - 294px);
    left: 294px;
  }
  
  .sidebar-closed ~ .chat-container .message-container {
    width: calc(100% - 350px);
    left: 0;
  }

  /* Make sure the chat content area is maximized */
  .chat-content {
    max-width: 100%;
    padding: 20px; /* Slightly reduced padding */
  }

  /* Ensure message bubbles adapt to container width */
  .message-bubble {
    max-width: 95%;
  }

  /* Adjust the document details for smaller resource panel */
  .document-name {
    max-width: 220px; /* Further reduce max width */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  /* Compact the resource items slightly */
  .resource-item {
    gap: 8px; /* Reduced from larger gap */
  }
  
  .content-wrapper {
    gap: 12px; /* Reduced from larger gap */
  }
}

@media (max-width: 1200px) {

  /* NEW: Reduce sidebar width */
  .sidebar-container {
    width: 250px; /* Reduced from 294px */
}

.bottom-section {
  padding: 8px; /* Reduced padding */
}

.plan-badge {
  font-size: 12px;
  padding: 4px 12px;
  margin-bottom: 6px;
}

.user-section {
  padding: 8px 12px;
  margin-bottom: 8px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  min-width: 36px; /* Ensure it doesn't shrink */
}

.user-info {
  max-width: 150px; /* Limit width for text overflow */
}

.user-email {
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bottom-links {
  gap: 12px;
  font-size: 12px;
}

/* Resources container - make it even smaller */
.resources-container {
    width: 300px;
}

/* NEW: Chat container with adjusted sidebar width */
.chat-container {
    width: calc(100% - 250px - 300px); /* Using new sidebar width */
    margin-left: 250px; /* Match new sidebar width */
}

.chat-container.sidebar-closed {
    width: calc(100% - 300px);
    margin-left: 0;
}

/* NEW: Adjust message container for new sidebar width */
.message-container {
    left: 250px; /* Match new sidebar width */
    width: calc(100% - 300px - 250px); /* With new sidebar width */
}

/* Header title size adjustment */
.header-title {
    font-size: 24px;
}

/* User section adjustments */
.user-section span {
    display: none;
}

/* File list container */
.file-list-container {
    width: 100%;
    margin-left: 0;
}

/* Header section spacing */
.d-flex.justify-content-between.align-items-center.mb-3 {
    padding: 0.5rem 0;
}

/* Document name truncation */
.document-name {
    max-width: 200px;
}

/* NEW: Adjust sidebar internal elements for smaller width */
.sidebar .logo-text {
    font-size: 24px;
}

.sidebar-files li {
    width: 100%;
}

.sidebar-files li .file-name {
    max-width: 150px;
}
}


@media (max-width: 932px) {
/* Sidebar positioning for mobile - hide off-screen by default */
.sidebar-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 294px !important;
  height: 100% !important;
  z-index: 1050 !important;
  transform: translateX(-100%) !important;
  transition: transform 0.3s ease !important;
}

/* Use !important to override any competing styles */
.sidebar-container.open {
  transform: translateX(0) !important;
}

/* Make resources trigger visible */
.resources-trigger {
  display: block !important;
  position: absolute;
  right: 20px;
  top: 10px;
  transform: none;
  background: transparent;
  border: none;
  color: var(--primary-dark);
  font-size: 24px;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 1051;
}

/* Resources container positioning */
.resources-container {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  width: 80% !important;
  height: 100% !important;
  z-index: 1050 !important;
  transform: translateX(100%) !important;
  transition: transform 0.3s ease !important;
}

/* Resources container when shown */
.resources-container.show {
  transform: translateX(0) !important;
}

/* Hide section divider */
.section-divider {
    display: none;
}

/* Full width chat container */
.chat-container {
    width: 100% !important;
    margin-left: 0 !important;
}

/* Message container takes full width */
.message-container {
    width: 100% !important;
    left: 0 !important;
}

/* File upload modal adjustments */
#fileUploadModal .modal-dialog {
    min-height: calc(100vh - 60px);
    display: flex;
    align-items: center;
    margin: auto;
}

#fileUploadModal .modal-content {
    max-height: 90vh;
    width: 90vw;
    margin: auto;
}

/* Adjust file list container height */
.file-list-container {
    max-height: calc(100vh - 445px);
}

/* Resource content padding */
.resource-content {
    padding: 16px;
}

/* Source info layout */
.source-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
}

/* Bullet line height for resources */
.bullet-line {
    height: calc(100% + 64px);
}

/* Document name full width on mobile */
.document-name {
    max-width: 100%;
}

/* Feedback modal adjustments */
.feedback-modal-content {
    padding: 24px;
    width: 95%;
}

.form-group {
    margin-bottom: 16px;
}

.form-control {
    padding: 10px;
}
}

@media (max-width: 510px) {
  .sources-box {
    width: 53px;
  }

  .sources-box .sources-text {
    display: none;
  }

  .feedback-modal-content {
    padding: 24px;
  }

  .feedback-modal-footer {
    flex-direction: column;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
    text-align: center;

    .feedback-modal-content {
      padding: 20px;
    }

    .feedback-modal-header h3 {
      font-size: 20px;
    }

    .form-control[type="file"]::before {
      width: 100%;
      text-align: center;
    }
  }
}

@media (max-width:550px) {
  .domain-name {
    max-width: 250px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

@media (max-height: 667px) {
  #fileUploadModal .modal-dialog {
    height: 100vh;
    display: flex;
    align-items: center;
    margin: 0 auto;
  }

  #fileUploadModal .modal-content {
    max-height: 95vh;
    overflow-y: auto;
  }

  .file-list {
    max-height: 35vh;
  }

  .upload-area {
    padding: 40px 30px;
    min-height: auto;
  }

  .upload-icon-wrapper {
    width: 90px;
    height: 90px;
    margin-bottom: 16px;
  }

  .upload-icon i {
    font-size: 36px;
  }

  .upload-content h5 {
    font-size: 24px;
    margin-bottom: 12px;
  }

  .chat-container {
    height: 98%;
  }
}

@media (max-width: 430px) {
  .new-domain-input-card {
    display: flex;
    align-items: center;
  }

  .new-domain-actions {
    display: flex;
    gap: 4px;
    margin-left: 8px;
  }

  .confirm-button,
  .cancel-button {
    padding: 4px;
  }

  .domain-name {
    max-width: 200px;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .upload-container {
    padding: 0;
  }
  .domain-info h6 {
    max-width: 137px;
  }
}

/* URL Modal */

.url-input-btn {
  background: transparent;
  border: 1px solid var(--primary-dark);
  color: var(--primary-dark);
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.url-input-btn:hover {
  background: var(--primary-light);
  border-color: var(--primary-dark);
}

.url-input-btn i {
  font-size: 1.2rem;
}

.url-input-container {
  background: var(--sidebar-bg);
  border-radius: 12px;
  padding: 1.5rem;
}

.url-input {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: white !important;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.url-input:focus {
  outline: none;
  border-color: var(--primary-dark) !important;
  background: rgba(255, 255, 255, 0.08) !important;
}

.url-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.add-url-btn {
  width: 100%;
  padding: 12px;
  background: var(--primary-dark);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.add-url-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
}

.add-url-btn:not(:disabled):hover {
  background: var(--primary-dark);
}